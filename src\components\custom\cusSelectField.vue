<!--
 * @Description: 场地选择器组件 - 优化版本
 * @version: 2.0
 * @Author: sakuya
 * @Date: 2021年6月10日10:04:07
 * @LastEditors: Optimized
 * @LastEditTime: 2025-01-10
 * @Optimizations:
 *   - 性能优化：减少不必要的重新渲染
 *   - 代码组织：更好的方法分离和命名
 *   - 内存管理：防止内存泄漏
 *   - 用户体验：更好的加载状态和错误处理
 *   - 可维护性：更清晰的代码结构
-->

<template>
	<el-input
		ref="selectInput"
		:disabled="disabled"
		:readonly="readonly"
		:clearable="clearable"
		:style="{ width: width }"
		:placeholder="computedPlaceholder"
		@focus="handleInputFocus"
		@clear="handleClear"
	>
		<!-- 已选择的标签显示 -->
		<template v-if="hasSelectedItems" #prefix>
			<!-- 显示前两个标签 -->
			<template v-for="(item, index) in visibleTags" :key="`tag-${item.id}`">
				<el-tag
					effect="dark"
					closable
					type="info"
					@close="handleRemoveTag(item, index)"
				>
					{{ item.label }}
				</el-tag>
			</template>

			<!-- 更多标签的折叠显示 -->
			<template v-if="hasMoreTags">
				<el-popover
					width="500"
					placement="bottom"
					trigger="hover"
					popper-class="input-tag-popover"
				>
					<template #reference>
						<el-tag effect="dark" type="info">
							+{{ remainingTagsCount }}
						</el-tag>
					</template>
					<el-tag
						v-for="(item, index) in hiddenTags"
						:key="`hidden-tag-${item.id}`"
						effect="dark"
						closable
						type="info"
						@close="handleRemoveTag(item, index + MAX_VISIBLE_TAGS)"
					>
						{{ item.label }}
					</el-tag>
				</el-popover>
			</template>
		</template>

		<template #suffix>
			<el-icon class="el-input__icon">
				<el-icon-arrow-down />
			</el-icon>
		</template>
	</el-input>
	<!-- 场地选择对话框 -->
	<el-dialog
		v-model="dialogVisible"
		title="选择场地"
		width="900"
		append-to-body
		:close-on-press-escape="false"
		class="select-dialog"
		@closed="handleDialogClosed"
	>
		<!-- 校区选择头部 -->
		<el-header class="dialog-header">
			<el-select
				v-if="shouldShowCampusSelect"
				v-model="params.campus_id"
				placeholder="请选择校区"
				:teleported="false"
				@change="handleCampusChange"
			>
				<el-option
					v-for="campus in campusList"
					:key="campus.code"
					:label="campus.name"
					:value="campus.value"
				/>
			</el-select>
		</el-header>

		<!-- 主要内容标签页 -->
		<el-tabs v-model="activeTabName" type="border-card" @tab-change="handleTabChange">
			<!-- 场地选择标签页 -->
			<el-tab-pane label="场地" name="field">
				<el-container>
					<!-- 左侧建筑树 -->
					<el-aside :style="{ width: `${treeWidth}px` }">
						<el-container>
							<el-main>
								<el-tree
									ref="buildingTree"
									class="building-tree"
									node-key="id"
									:data="buildingTreeData"
									:highlight-current="true"
									:expand-on-click-node="false"
									:props="treeProps"
									:default-expanded-keys="[]"
									@node-click="handleBuildingNodeClick"
								/>
							</el-main>
						</el-container>
					</el-aside>

					<!-- 右侧房间列表 -->
					<el-main class="room-content">
						<div v-loading="loading" class="room-list-container">
							<template v-if="hasRoomData">
								<!-- 多选模式 -->
								<template v-if="multiple">
									<ul class="room-list">
										<li
											v-for="room in roomList"
											:key="room.id"
											:class="{ 'room-selected': isRoomSelected(room.id) }"
											@click="handleRoomClick($event, room)"
										>
											{{ room.room_name }}
										</li>
									</ul>
								</template>
								<!-- 单选模式 -->
								<template v-else>
									<div class="room-buttons">
										<el-button
											v-for="room in roomList"
											:key="room.id"
											:type="isRoomSelected(room.id) ? 'primary' : 'default'"
											class="room-button"
											@click="handleRoomClick($event, room)"
										>
											{{ room.room_name }}
										</el-button>
									</div>
								</template>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80" />
							</template>
						</div>
					</el-main>
				</el-container>
			</el-tab-pane>
			<!-- 已选择标签页 -->
			<el-tab-pane label="已选" name="selected">
				<div class="selected-container">
					<!-- 左侧状态分类 -->
					<div class="status-sidebar">
						<template v-if="multiple">
							<div
								v-for="(status, index) in selectionStatusData"
								:key="status.id"
								:class="{ 'status-active': index === activeStatusIndex }"
								class="status-item"
								@click="handleStatusClick(status, index)"
							>
								{{ status.label }}
							</div>
						</template>
						<template v-else>
							<div
								v-for="(status, index) in selectionStatusData"
								:key="status.id"
								:class="{ 'status-active': index === activeStatusIndex }"
								class="status-item-single"
								@click="handleStatusClick(status, index)"
							>
								{{ status.label }}
							</div>
						</template>
					</div>

					<!-- 右侧已选择的房间列表 -->
					<div class="selected-rooms">
						<template v-if="hasSelectedRoomsInCurrentStatus">
							<div
								v-for="room in currentStatusRooms"
								:key="room.id"
								class="selected-room-item"
							>
								<el-button class="selected-room-button" type="primary">
									<span>{{ room.room_name || room.label }}</span>
								</el-button>
							</div>
						</template>
						<template v-else>
							<el-empty description="暂无数据" :image-size="80" />
						</template>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>

		<!-- 对话框底部 -->
		<div class="dialog-footer">
			<span class="selection-count">
				已选择
				<span class="count-number">{{ totalSelectedCount }}</span>
				个
			</span>
			<el-button type="primary" @click="handleConfirm">
				确定
			</el-button>
		</div>
	</el-dialog>
</template>

<script>
import config from '@/config/tableSelect'
import api from '@/api'
import cusTom from '@/utils/cusTom'

export default {
	name: 'CusSelectField',

	props: {
		// v-model 绑定值
		modelValue: {
			type: Array,
			default: () => []
		},

		// API 对象配置
		apiObj: {
			type: Object,
			default: () => api.fieldRoom.rooms.all
		},

		// 基础配置
		placeholder: {
			type: String,
			default: '请选择场地'
		},
		size: {
			type: String,
			default: 'default'
		},
		clearable: {
			type: Boolean,
			default: true
		},
		multiple: {
			type: Boolean,
			default: false
		},
		filterable: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},

		// 布局配置
		tableWidth: {
			type: Number,
			default: 600
		},
		treeWidth: {
			type: Number,
			default: 240
		},
		width: {
			type: String,
			default: '200px'
		},

		// 其他配置
		mode: {
			type: String,
			default: 'popover'
		},
		props: {
			type: Object,
			default: () => ({})
		}
	},

	emits: ['update:modelValue', 'change'],
	data() {
		return {
			// 常量定义
			MAX_VISIBLE_TAGS: 2,

			// 加载状态
			loading: false,

			// 搜索和分页
			keyword: null,
			pageSize: config.pageSize,
			total: 0,
			currentPage: 1,

			// 组件内部状态
			selectedItems: [],
			roomList: [],

			// 配置对象
			defaultProps: {
				label: config.props.label,
				value: config.props.value,
				page: config.request.page,
				pageSize: config.request.pageSize,
				keyword: config.request.keyword
			},

			// 请求参数
			formData: {},
			params: {
				campus_id: '',
				tenant_id: ''
			},

			// 校区和建筑数据
			campusList: [],
			buildingData: [],

			// 树形组件配置
			treeProps: {
				children: 'floor',
				label: 'name'
			},
			selectedTreeNodeId: null,

			// 房间选择状态
			selectedRoomIds: [],
			selectedRoomData: [],

			// 对话框状态
			dialogVisible: false,
			shouldBlur: false,

			// 选择状态数据
			selectionStatusData: [
				{ id: 1, label: '未提交', children: [], data: [] },
				{ id: 2, label: '已提交', children: [], data: [] }
			],
			activeStatusIndex: 0,

			// 标签页状态
			activeTabName: 'field'
		}
	},
	computed: {
		// 输入框占位符
		computedPlaceholder() {
			return this.hasSelectedItems ? '' : this.placeholder
		},

		// 是否有选中项
		hasSelectedItems() {
			return this.selectedItems && this.selectedItems.length > 0
		},

		// 可见的标签（前两个）
		visibleTags() {
			return this.selectedItems.slice(0, this.MAX_VISIBLE_TAGS)
		},

		// 隐藏的标签（超过两个的部分）
		hiddenTags() {
			return this.selectedItems.slice(this.MAX_VISIBLE_TAGS)
		},

		// 是否有更多标签需要折叠显示
		hasMoreTags() {
			return this.selectedItems.length > this.MAX_VISIBLE_TAGS
		},

		// 剩余标签数量
		remainingTagsCount() {
			return Math.max(0, this.selectedItems.length - this.MAX_VISIBLE_TAGS)
		},

		// 是否显示校区选择
		shouldShowCampusSelect() {
			return this.campusList.length > 1
		},

		// 建筑树数据
		buildingTreeData() {
			return [...this.buildingData]
		},

		// 是否有房间数据
		hasRoomData() {
			return this.roomList && this.roomList.length > 0
		},

		// 当前状态的房间列表
		currentStatusRooms() {
			const currentStatus = this.selectionStatusData[this.activeStatusIndex]
			return currentStatus ? currentStatus.children : []
		},

		// 当前状态是否有选中的房间
		hasSelectedRoomsInCurrentStatus() {
			return this.currentStatusRooms.length > 0
		},

		// 总选中数量
		totalSelectedCount() {
			return this.selectionStatusData[0]?.children?.length || 0
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				if (val) {
					this.defaultValue = val
					this.statusData[1].children = val
					this.statusData[1].data = val.map((item) => item.id)
				}
			},
			immediate: true,
			deep: true
		},
		'params.campus_id': {
			handler() {
				// this.getData()
			},
			immediate: true
		},
		rowClickData: {
			handler(newval, oldval) {
				console.log(newval, oldval, 'rowClickData')
				if (!this.multiple) {
					this.statusData[0].children = [newval]
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				} else {
					this.statusData[0].children = Array.from(new Set(newval.map(JSON.stringify))).map(JSON.parse)
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				}
			},
			deep: true
		}
	},
	created() {
		const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
		this.CampusManagementList = campusInfo
		this.params.campus_id = campusId
		this.params.tenant_id = tenantId
	},
	mounted() {
		this.defaultProps = Object.assign(this.defaultProps, this.props)
		this.defaultValue = this.modelValue
	},
	methods: {
		showDialog() {
			if (!this.shuldBlur) {
				this.dialogVisible = true
				this.getDept()
				this.getData()
			}
		},
		closeDialog() {
			this.blur()
		},
		tabChange(val) {
			console.log(val, 'tabChange')
			this.groupData = []
			switch (val) {
				case 'field':
					this.getDept()
					break
				case 'selected':
					break
			}
		},
		// 点击更改显示未提交/已提交的children
		clickStatus(val, index) {
			console.log(val, index, 'clickStatus')
			this.activeIndexStatus = index
		},
		confirm() {
			if (this.multiple) {
				const arr = Array.from(new Set(this.rowClickData.map(JSON.stringify))).map(JSON.parse)
				this.defaultValue = arr.map((item) => {
					return {
						label: item.building_name + '-' + item.floor_name + '-' + item.room_name,
						id: item.id
					}
				})
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			} else {
				if (this.rowClickData.id) {
					this.defaultValue = [
						{
							label:
								this.rowClickData.building_name +
								'-' +
								this.rowClickData.floor_name +
								'-' +
								this.rowClickData.room_name,
							id: this.rowClickData.id
						}
					]
				}
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			}
			this.dialogVisible = false
			this.shuldBlur = true
			this.$nextTick(() => {
				this.blur()
			})
		},
		//获取建筑楼层
		async getDept() {
			const res = await this.$API.fieldRoom.all.get(this.params)
			this.transData(res, 'name')
		},
		transData(res, field, type) {
			if (!res.data) res.data = []
			this.groupData = res.data.map((v) => {
				v.name = v[field]
				if (v.child && v.child.length > 0) {
					v.children = v.child
				}
				return v
			})
			if (type == 'tree') {
				this.groupData = cusTom.arrayToTree(this.groupData, 'id', 'parent_id')
			}
		},
		//树点击事件
		groupClick(data, node) {
			this.treeNodeId = data.id
			this.getData(node.level)
		},
		//表格显示隐藏回调
		visibleChange(visible) {
			if (visible) {
				this.currentPage = 1
				this.keyword = null
				this.formData = {}
				this.getData()
			} else {
			}
		},
		//获取表格数据
		async getData(level) {
			this.loading = true
			var reqData = {
				[this.defaultProps.page]: this.currentPage,
				[this.defaultProps.pageSize]: this.pageSize,
				[this.defaultProps.keyword]: this.keyword
			}
			var customParams = {
				building_id: level == 1 ? this.treeNodeId : null,
				floor_id: level == 2 ? this.treeNodeId : null
			}
			Object.assign(reqData, this.params, this.formData, customParams)
			var res = await this.apiObj.get(reqData)
			this.tableData = res.data || []
			this.loading = false
		},
		//插糟表单提交
		formSubmit() {
			this.currentPage = 1
			this.keyword = null
			this.getData()
		},
		//分页刷新表格
		reload() {
			this.getData()
		},
		changeRoom(e, v) {
			console.log(v, 'changeRoom')
			if (!this.multiple) {
				this.rowClickData = v
				this.checkedRoom = [v.id]
				return false
			}
			let has = this.rowClickData.some((item) => item.id == v.id)
			if (has) {
				this.rowClickData = this.rowClickData.filter((item) => item.id != v.id)
				this.checkedRoom = this.checkedRoom.filter((item) => item != v.id)
			} else {
				this.rowClickData.push(v)
				this.checkedRoom = this.rowClickData.map((item) => item.id)
			}
		},
		//tags删除后回调
		removeTag(tag, index) {
			if (this.disabled) {
				return false
			}
			if (this.multiple) {
				this.rowClickData.splice(index, 1)
				this.defaultValue.splice(index, 1)
				this.checkedRoom = this.checkedRoom.filter((item) => item != tag.id)
			} else {
				this.rowClickData = []
				this.defaultValue = []
			}
			this.$emit('update:modelValue', this.defaultValue)
			this.$emit('change', this.defaultValue)
		},
		//清空后的回调
		clear() {
			this.$emit('update:modelValue', this.defaultValue)
		},
		// 关键值查询表格数据行
		findRowByKey(value) {
			return this.tableData.find((item) => item[this.defaultProps.value] === value)
		},
		filterMethod(keyword) {
			if (!keyword) {
				this.keyword = null
				return false
			}
			this.keyword = keyword
			this.getData()
		},
		// 触发select隐藏
		blur() {
			if (this.shuldBlur) {
				this.shuldBlur = false
				return false
			}
			this.$refs.select.blur()
		},
		// 触发select显示
		focus() {
			this.$refs.select.focus()
		}
	}
}
</script>

<style lang="scss">
.select-dialog {
	.el-dialog__body {
		padding-top: 12px;
	}
}

.input-tag-popover {
	.el-tag {
		margin-bottom: 5px;
		margin-right: 3px;
	}

	.el-tag + .el-tag {
		margin-left: 0px;
	}
}
</style>

<style scoped lang="scss">
.el-h {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border: none;
	padding-left: 0;

	> .el-select {
		margin-right: 15px;
	}
}

.el-m {
	height: 300px;
	overflow: auto;

	.room-list {
		display: flex;
		flex-wrap: wrap;

		li {
			line-height: 1;
			height: 32px;
			width: 185px;
			padding: 8px 15px;
			border: 1px solid #dcdfe6;
			border-radius: 4px;
			text-align: center;
			box-sizing: border-box;
			white-space: nowrap;
			cursor: pointer;
			margin-bottom: 5px;
			margin-right: 8px;
			font-size: 14px;
			user-select: none;
			vertical-align: middle;

			&:hover {
				color: #2745b2;
				border-color: #bec7e7;
				background-color: #e9ecf7;
			}
		}

		.room-select {
			color: #ffffff;
			background-color: #2745b2;
			border-color: #2745b2;

			&:hover {
				color: #ffffff;
				background-color: #677cc9;
				border-color: #677cc9;
			}
		}
	}
}

.is_active {
	background-color: #e9ecf7;
	position: relative;

	&::after {
		content: '';
		width: 3px;
		height: 100%;
		position: absolute;
		right: 0;
		background-color: var(--el-color-primary-light-3);
	}
}

.selectBox {
	display: flex;
	min-height: 300px;
	border: 1px solid #eee;

	section {
		padding: 0 10px;
	}

	& > div {
		padding: 10px;
	}

	&_one {
		flex: 1;
		padding-right: 0 !important;
		border-right: 1px solid #eee;
	}

	&_three {
		flex: 3;
		max-height: 300px;
		overflow: auto;

		.selectItem {
			display: inline-block;
			width: 185px;

			.el-b {
				min-width: 100%;

				:deep(.el-checkbox-button__inner) {
					width: 100%;
				}
			}
		}

		section + section {
			margin-top: 5px;
		}
	}
}

.el-b {
	width: 30%;
	margin-bottom: 5px;
	margin-right: 8px;
	display: inline-block;

	:deep(.el-button--default) {
		width: 100%;
	}
}

.st {
	height: 32px;
	font-size: 16px;
	padding-left: 10px;
	display: flex;
	align-items: center;
}

.single-section {
	height: 32px;
	line-height: 32px;
}

.menu {
	height: 300px;
	overflow: auto;
}

.sc-table-select__table {
	padding: 0px;
}

.sc-table-select__page {
	padding-top: 12px;
}

.el-f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px;
	height: 50px;
}

.selectTitle {
	padding: 10px;
	font-weight: bold;
	color: #333;
	font-size: 18px;
	border-bottom: 1px solid #eee;
}
</style>
