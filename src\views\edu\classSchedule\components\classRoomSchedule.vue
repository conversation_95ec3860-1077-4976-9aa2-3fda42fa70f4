<template>
	<el-container>
		<el-main>
			<div class="searchContent">
				<cusSelectField v-model="room" :multiple="false" style="width: 240px;" placeholder="请选择教室" @change="roomChange"></cusSelectField>
				<el-button type="primary" icon="el-icon-search" @click="upSearch">搜索</el-button>
				<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
			</div>
			<div class="week-range">
				<weekRange @change="weekChange"></weekRange>
			</div>
			<div v-if="roomSchedule">
				<div class="schedule-content" v-for="classItem in roomSchedule.class_list">
					<div class="class-name">{{ classItem.class_name }}</div>
					<ul class="table th">
						<li class="row">
							<div class="row-data">
								<span class="cell">时间 / 日期</span>
								<span v-for="item in roomSchedule.date_list" :key="item" class="cell"
									>{{ item.attend_day }}<br />（{{ item.date }}）</span
								>
							</div>
						</li>
					</ul>
					<ul class="table td">
						<li v-for="item in classItem.rows" class="row">
							<div class="row-data">
								<span class="cell">{{ item.item_name }}<br />{{ item.begin_time + '~' + item.end_time }}</span>
								<span v-for="items in item.cells" :key="items" class="cell">
									<div v-if="items.timetable" class="cell-info" :class="items.timetable ? 'bg' : ''">
										<p>{{ items.timetable.grade_name?.name + items.timetable.class_info?.name || '' }}</p>
										<p>{{ items.timetable.course_info?.name }}（{{ items.timetable.teacher_info?.name }}）</p>
										<p>{{ items.timetable.room_info?.name }}</p>
										<el-icon size="20" color="red" class="close-icon" @click="removeItem(items)"
											><CircleClose
										/></el-icon>
									</div>
									<div v-else class="edit-cell-info" @click="addItem(item, items)">
										<el-icon size="20" class="add-icon"><Plus /></el-icon>
									</div>
								</span>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</el-main>
	</el-container>
</template>
<script setup>
import cusSelectField from '@/components/custom/cusSelectField.vue'
import weekRange from './weekRange.vue'
import { Plus, CircleClose } from '@element-plus/icons-vue'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		campus_id: campusId,
		tenant_id: tenantId,
		room_id: null,
		start_date: null,
		end_date: null
	}
}
const room = ref(null)

const params = ref(defaultParams())
const weekChange = (val) => {
	params.value.start_date = val[0]
	params.value.end_date = val[1]
	if (params.value.room_id) {
		getRoomScheduleData()
	}
}
const roomChange = (val) => {
	console.log(val)
	params.value.room_id = val[0].id
	getRoomScheduleData()
}
const upSearch = () => {
	getRoomScheduleData()
}
const refresh = () => {
	params.value = defaultParams()
	upSearch()
}

// 课表操作
const removeItem = (item) => {
	item.timetable = null
}
const addItem = (item, items) => {
	console.log(item, items, '添加')
}

// 获取班级课表
const roomSchedule = ref()
const getRoomScheduleData = () => {
	globalPropValue.classSchedule.getClassRoomSchedule.get({ ...params.value }).then((res) => {
		if (res.code === 200) {
			roomSchedule.value = res.data
		}
	})
}
</script>
<style lang="scss" scoped>
.searchContent {
	display: flex;
	gap: 10px;
	align-items: center;
	margin-bottom: 10px;
}

.week-range {
	display: flex;
	justify-content: center;
}
.bg {
	background-color: var(--el-color-info-light-9);
}

.schedule-content {
	min-height: 350px;
	margin-bottom: 10px;

	.class-name {
		font-size: 16px;
		font-weight: 600;
		padding: 10px;
		text-align: center;
	}

	.table {
		width: 100%;
		display: flex;

		.row {
			// border-left: 1px solid var(--el-border-color-light);
			// border-right: 1px solid var(--el-border-color-light);
			border-bottom: 1px solid var(--el-border-color-light);

			.row-data {
				display: flex;

				.bg1.selected {
					color: var(--el-color-primary);
					background-color: var(--el-color-primary-light-8);
				}

				.bg3.selected {
					color: var(--el-color-success);
					background-color: var(--el-color-success-light-8);
				}

				.bg4.selected {
					color: var(--el-color-warning);
					background-color: var(--el-color-warning-light-8);
				}

				.bg5.selected {
					color: var(--el-color-info);
					background-color: var(--el-color-info-light-8);
				}

				.bg6.selected {
					color: var(--el-color-danger);
					background-color: var(--el-color-danger-light-8);
				}
			}

			.cell {
				flex: 1;
				min-width: 100px;
				cursor: pointer;
				// border-right: 1px solid var(--el-border-color-light);

				&:first-child {
					flex: 0;
					min-width: 90px;
				}

				&:last-child {
					border-right: none;
				}
			}
		}
	}

	.th {
		.row {
			width: 100%;
			border-top: 1px solid var(--el-border-color-light);
			background-color: var(--el-border-color-extra-light);
		}

		.row-data {
			width: 100%;
			height: 50px;

			.cell {
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
			}
		}
	}

	.td {
		flex-direction: column;

		.row {
			.row-data {
				min-height: 90px;

				.cell {
					p {
						margin-bottom: 5px;
						font-size: 12px;

						&:first-child {
							font-size: 14px;
							font-weight: bold;
						}
					}

					&:not(:first-child) {
						padding: 5px;
					}

					&:first-child {
						font-weight: bold;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						background-color: var(--el-border-color-extra-light);
					}

					.cell-info {
						height: 100%;
						padding: 10px;
						position: relative;
						.close-icon {
							position: absolute;
							right: 5px;
							top: 5px;
							display: none;
						}
						&:hover {
							.close-icon {
								display: block;
							}
						}
					}
				}
				.edit-cell-info {
					cursor: pointer;
					height: 100%;
					display: flex;
					justify-content: center;
					align-items: center;
					.add-icon {
						display: none;
					}
					&:hover {
						background-color: var(--el-border-color-extra-light);
						.add-icon {
							display: block;
							color: var(--el-color-primary);
						}
					}
				}
			}
		}
	}
}
</style>
