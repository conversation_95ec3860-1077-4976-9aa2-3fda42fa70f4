<template>
	<div class="week-range-container">
		<div class="week-navigation">
			<!-- 上一周按钮 -->
			<el-button
				type="default"
				:size="props.size"
				:disabled="props.disabled"
				@click="goToPreviousWeek"
				:icon="ArrowLeft"
			>
				上周
			</el-button>

			<!-- 本周按钮 -->
			<el-button
				:type="isCurrentWeek ? 'primary' : 'default'"
				:size="props.size"
				:disabled="props.disabled"
				@click="goToCurrentWeek"
			>
				本周
			</el-button>

			<!-- 下一周按钮 -->
			<el-button type="default" :size="props.size" :disabled="props.disabled" @click="goToNextWeek">
				下周
				<el-icon class="el-icon--right">
					<ArrowRight />
				</el-icon>
			</el-button>
		</div>

		<!-- 时间范围显示 -->
		<div class="time-range-display">
			<span class="time-range-label">上课时间：</span>
			<span class="time-range-value">{{ formatDateRange }}</span>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// Props
const props = defineProps({
	modelValue: {
		type: Array,
		default: () => []
	},
	// 是否显示时间范围文本
	showTimeRange: {
		type: Boolean,
		default: true
	},
	// 时间范围标签文本
	timeRangeLabel: {
		type: String,
		default: '上课时间：'
	},
	// 是否禁用
	disabled: {
		type: Boolean,
		default: false
	},
	// 按钮尺寸
	size: {
		type: String,
		default: 'default',
		validator: (value) => ['large', 'default', 'small'].includes(value)
	},
	// 日期格式
	format: {
		type: String,
		default: 'YYYY-MM-DD'
	},
	// 显示格式
	displayFormat: {
		type: String,
		default: 'YYYY-MM-DD'
	}
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 当前选择的周的开始日期
const currentWeekStart = ref(dayjs().startOf('week'))

// 计算当前周的日期范围
const currentWeekRange = computed(() => {
	const start = currentWeekStart.value
	const end = start.add(6, 'day')
	return [start.format(props.format), end.format(props.format)]
})

// 格式化显示的日期范围
const formatDateRange = computed(() => {
	const start = currentWeekStart.value
	const end = start.add(6, 'day')
	return `${start.format(props.displayFormat)}~${end.format(props.displayFormat)}`
})

// 判断是否是当前周
const isCurrentWeek = computed(() => {
	const thisWeekStart = dayjs().startOf('week')
	return currentWeekStart.value.isSame(thisWeekStart, 'day')
})

// 上一周
const goToPreviousWeek = () => {
	currentWeekStart.value = currentWeekStart.value.subtract(1, 'week')
	updateValue()
}

// 下一周
const goToNextWeek = () => {
	currentWeekStart.value = currentWeekStart.value.add(1, 'week')
	updateValue()
}

// 回到本周
const goToCurrentWeek = () => {
	currentWeekStart.value = dayjs().startOf('week')
	updateValue()
}

// 更新值并触发事件
const updateValue = () => {
	const range = currentWeekRange.value
	emit('update:modelValue', range)
	emit('change', range)
}

// 监听外部传入的值变化
watch(
	() => props.modelValue,
	(newValue) => {
		if (newValue && newValue.length === 2) {
			const startDate = dayjs(newValue[0])
			if (startDate.isValid()) {
				currentWeekStart.value = startDate.startOf('week')
			}
		}
	},
	{ immediate: true }
)

// 暴露方法给父组件
defineExpose({
	goToPreviousWeek,
	goToNextWeek,
	goToCurrentWeek,
	getCurrentWeekRange: () => currentWeekRange.value,
	setWeekByDate: (date) => {
		const targetDate = dayjs(date)
		if (targetDate.isValid()) {
			currentWeekStart.value = targetDate.startOf('week')
			updateValue()
		}
	}
})

// 初始化
if (!props.modelValue || props.modelValue.length === 0) {
	  updateValue()
}
</script>

<style scoped>
.week-range-container {
	display: flex;
	align-items: center;
	gap: 16px;
	padding: 8px 0;
}

.week-navigation {
	display: flex;
	align-items: center;
	gap: 8px;
}

.week-navigation .el-button {
	min-width: 60px;
}

.time-range-display {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #606266;
}

.time-range-label {
	margin-right: 4px;
	font-weight: 500;
}

.time-range-value {
	color: #409eff;
	font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.week-range-container {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.week-navigation .el-button {
		min-width: 50px;
		font-size: 12px;
	}

	.time-range-display {
		font-size: 13px;
	}
}
</style>
